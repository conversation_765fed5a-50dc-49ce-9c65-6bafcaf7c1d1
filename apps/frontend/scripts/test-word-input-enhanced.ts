/**
 * 增强填词功能测试脚本
 * 🎯 测试目标：验证新的填词功能特性
 * 📦 测试范围：深灰色边框、Toast提醒、键盘快捷键、点击退出
 */

import { expect, test } from '@playwright/test';

test.describe('增强填词功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到应用
    await page.goto('/');

    // 等待页面加载
    await page.waitForLoadState('networkidle');

    // 切换到【颜色】【词语】模式
    await page.evaluate(() => {
      const { useMatrixStore } = require('@/core/matrix/MatrixStore');
      const store = useMatrixStore.getState();
      store.setMainMode('color');
      store.setContentMode('word');
    });

    // 等待模式切换完成
    await page.waitForTimeout(500);
  });

  test('双击格子显示深灰色加粗边框', async ({ page }) => {
    // 找到一个有颜色和级别的单元格
    const cell = page.locator('[data-x="5"][data-y="5"]').first();

    // 双击单元格
    await cell.dblclick();

    // 验证单元格是否有word-input-active类
    await expect(cell).toHaveClass(/word-input-active/);

    // 验证CSS样式是否正确应用
    const borderStyle = await cell.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return styles.border;
    });

    // 检查是否有深灰色边框
    expect(borderStyle).toContain('rgb(75, 85, 99)'); // #4b5563
  });

  test('词库为空时显示Toast提醒（中央黑白灰设计）', async ({ page }) => {
    // 清空所有词库
    await page.evaluate(() => {
      const { useWordLibraryStore } = require('@/core/wordLibrary/WordLibraryStore');
      const store = useWordLibraryStore.getState();
      // 清空所有词库
      store.clearAllLibraries();
    });

    // 双击单元格
    const cell = page.locator('[data-x="5"][data-y="5"]').first();
    await cell.dblclick();

    // 等待Toast出现
    await page.waitForTimeout(200);

    // 验证Toast消息是否显示
    const toastMessage = page.locator('text=请先在词库中添加词语');
    await expect(toastMessage).toBeVisible();

    // 验证Toast位置是否在中央
    const toastElement = page.locator('.fixed.top-1\\/2.left-1\\/2');
    await expect(toastElement).toBeVisible();

    // 验证Toast样式是否为黑白灰设计
    const toastContainer = toastElement.locator('div').first();
    const bgColor = await toastContainer.evaluate(el => {
      return window.getComputedStyle(el).backgroundColor;
    });
    expect(bgColor).toContain('rgb(17, 24, 39)'); // gray-900

    // 验证填词模式是否自动退出
    await expect(cell).not.toHaveClass(/word-input-active/);
  });

  test('键盘快捷键功能和实时显示', async ({ page }) => {
    // 先添加一些词语到词库
    await page.evaluate(() => {
      const { useWordLibraryStore } = require('@/core/wordLibrary/WordLibraryStore');
      const store = useWordLibraryStore.getState();
      store.addWord('red-1', '测试词语1');
      store.addWord('red-1', '测试词语2');
      store.addWord('red-1', '测试词语3');
    });

    // 双击激活填词模式
    const cell = page.locator('[data-x="5"][data-y="5"]').first();
    await cell.dblclick();

    // 验证填词模式激活
    await expect(cell).toHaveClass(/word-input-active/);

    // 验证初始显示第一个词语
    await expect(cell).toContainText('测试词语1');

    // 测试右键导航，应该实时显示下一个词语
    await page.keyboard.press('ArrowRight');
    await page.waitForTimeout(100);
    await expect(cell).toContainText('测试词语2');

    // 测试左键导航，应该实时显示上一个词语
    await page.keyboard.press('ArrowLeft');
    await page.waitForTimeout(100);
    await expect(cell).toContainText('测试词语1');

    // 测试回车确认
    await page.keyboard.press('Enter');

    // 验证填词模式退出
    await expect(cell).not.toHaveClass(/word-input-active/);

    // 验证词语是否绑定到单元格
    await page.evaluate(() => {
      const { useMatrixStore } = require('@/core/matrix/MatrixStore');
      const store = useMatrixStore.getState();
      store.setContentMode('word');
    });

    // 检查单元格是否显示词语
    await expect(cell).toContainText('测试词语1');
  });

  test('ESC键退出填词模式', async ({ page }) => {
    // 双击激活填词模式
    const cell = page.locator('[data-x="5"][data-y="5"]').first();
    await cell.dblclick();

    // 验证填词模式激活
    await expect(cell).toHaveClass(/word-input-active/);

    // 按ESC键
    await page.keyboard.press('Escape');

    // 验证填词模式退出
    await expect(cell).not.toHaveClass(/word-input-active/);
  });

  test('点击空白退出填词模式并清除临时显示', async ({ page }) => {
    // 先添加词语到词库
    await page.evaluate(() => {
      const { useWordLibraryStore } = require('@/core/wordLibrary/WordLibraryStore');
      const store = useWordLibraryStore.getState();
      store.addWord('red-1', '临时词语');
    });

    // 双击激活填词模式
    const cell = page.locator('[data-x="5"][data-y="5"]').first();
    await cell.dblclick();

    // 验证填词模式激活和临时显示
    await expect(cell).toHaveClass(/word-input-active/);
    await expect(cell).toContainText('临时词语');

    // 点击空白区域
    await page.click('body', { position: { x: 50, y: 50 } });

    // 验证填词模式退出
    await expect(cell).not.toHaveClass(/word-input-active/);

    // 验证临时显示被清除（单元格应该为空或显示原内容）
    const cellContent = await cell.textContent();
    expect(cellContent?.includes('临时词语')).toBe(false);
  });

  test('双击激活填词模式定位到已绑定词语', async ({ page }) => {
    // 先添加多个词语到词库
    await page.evaluate(() => {
      const { useWordLibraryStore, useMatrixStore } = require('@/core/wordLibrary/WordLibraryStore');
      const wordStore = useWordLibraryStore.getState();
      const matrixStore = useMatrixStore.getState();

      const word1Id = wordStore.addWord('red-1', '第一个词');
      const word2Id = wordStore.addWord('red-1', '第二个词');
      const word3Id = wordStore.addWord('red-1', '第三个词');

      // 绑定第二个词到单元格
      matrixStore.bindWordToCell(5, 5, word2Id);
    });

    // 双击激活填词模式
    const cell = page.locator('[data-x="5"][data-y="5"]').first();
    await cell.dblclick();

    // 验证填词模式激活
    await expect(cell).toHaveClass(/word-input-active/);

    // 验证显示的是已绑定的词语（第二个词）
    await expect(cell).toContainText('第二个词');
  });

  test('空词库双击激活填词模式滑动到可视区域并显示Toast', async ({ page }) => {
    // 清空词库
    await page.evaluate(() => {
      const { useWordLibraryStore } = require('@/core/wordLibrary/WordLibraryStore');
      const store = useWordLibraryStore.getState();
      store.clearAllLibraries();
    });

    // 双击激活填词模式
    const cell = page.locator('[data-x="5"][data-y="5"]').first();
    await cell.dblclick();

    // 验证填词模式激活
    await expect(cell).toHaveClass(/word-input-active/);

    // 验证Toast消息显示
    const toastMessage = page.locator('text=请填入词语');
    await expect(toastMessage).toBeVisible();

    // 验证词库高亮显示
    const libraryItem = page.locator('.word-library-active');
    await expect(libraryItem).toBeVisible();

    // 等待2秒后验证Toast消失
    await page.waitForTimeout(2100);
    await expect(toastMessage).not.toBeVisible();
  });

  test('词库高亮显示', async ({ page }) => {
    // 先添加词语到特定词库
    await page.evaluate(() => {
      const { useWordLibraryStore } = require('@/core/wordLibrary/WordLibraryStore');
      const store = useWordLibraryStore.getState();
      store.addWord('red-1', '测试词语');
    });

    // 双击激活填词模式
    const cell = page.locator('[data-x="5"][data-y="5"]').first();
    await cell.dblclick();

    // 查找对应的词库项
    const libraryItem = page.locator('.word-library-active');

    // 验证词库是否高亮
    await expect(libraryItem).toBeVisible();

    // 验证词库是否滑动到可视区域
    const isInViewport = await libraryItem.evaluate(el => {
      const rect = el.getBoundingClientRect();
      return rect.top >= 0 && rect.bottom <= window.innerHeight;
    });

    expect(isInViewport).toBe(true);
  });

  test('Delete键删除词语', async ({ page }) => {
    // 先绑定一个词语
    await page.evaluate(() => {
      const { useWordLibraryStore, useMatrixStore } = require('@/core/wordLibrary/WordLibraryStore');
      const wordStore = useWordLibraryStore.getState();
      const matrixStore = useMatrixStore.getState();

      const wordId = wordStore.addWord('red-1', '测试词语');
      matrixStore.bindWordToCell(5, 5, wordId);
    });

    // 双击激活填词模式
    const cell = page.locator('[data-x="5"][data-y="5"]').first();
    await cell.dblclick();

    // 按Delete键
    await page.keyboard.press('Delete');

    // 验证填词模式退出
    await expect(cell).not.toHaveClass(/word-input-active/);

    // 验证词语是否被删除
    await page.evaluate(() => {
      const { useMatrixStore } = require('@/core/matrix/MatrixStore');
      const store = useMatrixStore.getState();
      store.setContentMode('word');
    });

    // 检查单元格是否为空
    const cellContent = await cell.textContent();
    expect(cellContent?.trim()).toBe('');
  });
});
